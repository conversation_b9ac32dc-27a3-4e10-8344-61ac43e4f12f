import React, { useContext, useState } from 'react';
import { GlobalContext } from '../../contexts/GlobalContext';
import { MkdSDK } from '../../services/MkdSDK';
import { showToast } from '../../utils/toastUtils';

const JoinCommunityModal = ({ isOpen, onClose, community }) => {
  if (!isOpen) return null;

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    industry: "",
    employeeCount: "",
    annualRevenue: "",
    goals: {
      buildPartnerships: false,
      gainAccess: false,
      getResources: false,
      learnStrategies: false,
      findSolutions: false,
      other: false
    },
    otherGoal: "",
    businessChallenge: "",
    majorBreakthrough: "",
    whyJoin: "",
    meetingCommitment: "",
    additionalNotes: "",
    agreeToTerms: false,
    agreeToGuidelines: false
  });

  const handleSubmit = async (skipPayment = false) => {
    if (!formData.agreeToTerms || !formData.agreeToGuidelines) {
      showToast(globalDispatch, "Please agree to the terms and guidelines", 5000, "error");
      return;
    }

    // check if payment method is added
    const sdk = new MkdSDK();
    const response = await sdk.GetPaymentMethods();
    if (response.error) {
      showToast(globalDispatch, response.message, 5000, "error");
      return;
    }

    if (response.list.length === 0) {
      setShowPaymentModal(true);
      return;
    }

    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.JoinCommunity({
        community_id: community.id,
        // Only include required fields for now since API doesn't support new fields yet
        first_name: {
          value: formData.firstName
        },
        last_name: {
          value: formData.lastName
        },
        industry: {
          value: formData.industry
        },
        meeting_commitment: {
          value: formData.meetingCommitment
        },
        additional_notes: {
          value: formData.additionalNotes
        },
        skip_payment: skipPayment
      });
      
      if(!response.error) {
        showToast(globalDispatch, "Successfully joined community!", 5000, "success");
        onClose(true);
      } else {
        showToast(globalDispatch, response.message, 5000, "error");
      }
    } catch (err) {
      showToast(globalDispatch, err.message || "Failed to join community", 5000, "error");
    } finally {
      setLoading(false);
    }
  };

  const renderForm = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-[#eaeaea]">Join {community?.title?.value} Community</h2>
      {renderStepIndicator()}
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">First Name *</label>
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
            className="h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
            placeholder="Enter first name"
            required
          />
        </div>
        <div>
          <label className="mb-2 block text-sm text-[#b5b5b5]">Last Name *</label>
          <input
            type="text"
            value={formData.lastName}
            onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
            className="h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
            placeholder="Enter last name"
            required
          />
        </div>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">What industry is your business in? *</label>
        <select
          value={formData.industry}
          onChange={(e) => setFormData(prev => ({ ...prev, industry: e.target.value }))}
          className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
          required
        >
          <option value="">Select your industry</option>
          <option value="technology">Technology</option>
          <option value="finance">Finance</option>
          <option value="healthcare">Healthcare</option>
          <option value="education">Education</option>
          <option value="retail">Retail</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">How many employees does your business have? *</label>
        <select
          value={formData.employeeCount}
          onChange={(e) => setFormData(prev => ({ ...prev, employeeCount: e.target.value }))}
          className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
        >
          <option value="">Select range</option>
          <option value="1-10">1-10</option>
          <option value="11-50">11-50</option>
          <option value="51-200">51-200</option>
          <option value="201-500">201-500</option>
          <option value="500+">500+</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">What's your approximate annual revenue? *</label>
        <select
          value={formData.annualRevenue}
          onChange={(e) => setFormData(prev => ({ ...prev, annualRevenue: e.target.value }))}
          className="h-10 w-full appearance-none rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
        >
          <option value="">Select range</option>
          <option value="under_100k">under 100K</option>
          <option value="100k_500k">100K - 500K</option>
          <option value="500k_1m">500K - 1M</option>
          <option value="1m_5m">1M - 5M</option>
          <option value="5m_plus">5M+</option>
        </select>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">What are your top 3 goals for joining this community? *</label>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.buildPartnerships}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, buildPartnerships: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Build partnerships to grow my business</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.gainAccess}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, gainAccess: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Gain access to investors and capital partners</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.getResources}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, getResources: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Get access to new resources and opportunities</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.learnStrategies}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, learnStrategies: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Learn strategies to leverage relationships</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.findSolutions}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, findSolutions: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Find solutions to specific business challenges</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={formData.goals.other}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                goals: { ...prev.goals, other: e.target.checked }
              }))}
              className="mr-2"
            />
            <span className="text-sm text-[#eaeaea]">Other:</span>
          </label>
          {formData.goals.other && (
            <input
              type="text"
              value={formData.otherGoal}
              onChange={(e) => setFormData(prev => ({ ...prev, otherGoal: e.target.value }))}
              className="mt-2 h-10 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 text-[#eaeaea]"
              placeholder="Please specify"
            />
          )}
        </div>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">
          What is your biggest business challenge that needs solving sooner rather than later? *
        </label>
        <textarea
          value={formData.businessChallenge}
          onChange={(e) => setFormData(prev => ({ ...prev, businessChallenge: e.target.value }))}
          className="h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]"
          placeholder="Describe your challenge..."
        />
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">
          If you could achieve one major breakthrough in your business this year, what would it be? *
        </label>
        <textarea
          value={formData.majorBreakthrough}
          onChange={(e) => setFormData(prev => ({ ...prev, majorBreakthrough: e.target.value }))}
          className="h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]"
          placeholder="Describe your desired breakthrough..."
        />
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">
          Why do you want to be part of {community?.title?.value}? *
        </label>
        <textarea
          value={formData.whyJoin}
          onChange={(e) => setFormData(prev => ({ ...prev, whyJoin: e.target.value }))}
          className="h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]"
          placeholder="Share your reasons..."
        />
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">
          How much time can you realistically commit to meeting with members and engaging in potential deals each month? *
        </label>
        <div className="space-y-2">
          {["1-2 hours", "3-5 hours", "6+ hours"].map((option) => (
            <label key={option} className="flex items-center">
              <input
                type="radio"
                name="meetingCommitment"
                value={option.toLowerCase()}
                checked={formData.meetingCommitment === option.toLowerCase()}
                onChange={(e) => setFormData(prev => ({ ...prev, meetingCommitment: e.target.value }))}
                className="mr-2"
              />
              <span className="text-sm text-[#eaeaea]">{option}</span>
            </label>
          ))}
        </div>
      </div>

      <div>
        <label className="mb-2 block text-sm text-[#b5b5b5]">Is there anything else you'd like us to know?</label>
        <textarea
          value={formData.additionalNotes}
          onChange={(e) => setFormData(prev => ({ ...prev, additionalNotes: e.target.value }))}
          className="h-32 w-full rounded-lg border border-[#363636] bg-[#1e1e1e] p-4 text-[#eaeaea]"
          placeholder="Share any additional information..."
        />
      </div>

      <div className="flex justify-end">
        <button
          onClick={() => setStep(2)}
          className="rounded-lg bg-[#2e7d32] px-6 py-2 text-sm text-white"
        >
          Next
        </button>
      </div>
    </div>
  );

  // ... rest of the existing code (renderGuidelines, renderPayment, etc.) ...

  return (
    <div>
      {/* Render the form */}
    </div>
  );
};

export default JoinCommunityModal; 